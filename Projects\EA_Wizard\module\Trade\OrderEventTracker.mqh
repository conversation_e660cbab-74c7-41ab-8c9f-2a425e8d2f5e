#property strict

#include "../mql4-lib-master/Collection/Vector.mqh"
#include "../mql4-lib-master/Trade/Order.mqh"
#include "../mql4-lib-master/Trade/OrderTracker.mqh"

enum OrderTrackedType
{
    ORDER_TRACKED_NONE,
    ORDER_TRACKED_NEW,
    ORDER_TRACKED_ACTIVATED,
    ORDER_TRACKED_MODIFIED,
    ORDER_TRACKED_CLOSED,
    ORDER_TRACKED_PARTIALLY_CLOSED
};

class OrderEvent
{
private:
    OrderTrackedType m_type;
    Order* m_order;
public:
    OrderEvent(OrderTrackedType type, const Order* order) : m_type(type), m_order((Order*)order) {}
    OrderTrackedType getType() const { return m_type; }
    Order* getOrder() const { return m_order; }
};

class OrderEventQueue
{
private:
    Vector<OrderEvent*> m_queue;
public:
    OrderEventQueue(){}

    void Enqueue(OrderEvent* event)
    {
        m_queue.push(event);
    }

    OrderEvent* dequeue()
    {
        if (m_queue.isEmpty())
            return new OrderEvent(ORDER_TRACKED_NONE, NULL);

        return m_queue.shift();
    }

    OrderEvent* peek() const
    {
        if (m_queue.isEmpty())
            return new OrderEvent(ORDER_TRACKED_NONE, NULL);

        return m_queue[0];
    }

    int size() const { return m_queue.size(); }

    bool isEmpty() const { return m_queue.isEmpty(); }

    void clear() { m_queue.clear(); }

    bool contains(OrderEvent* event) const
    {
        for (int i = 0; i < m_queue.size(); i++)
        {
            if (m_queue[i] == event)
                return true;
        }
        return false;
    }
};

class OrderEventTracker : public OrderTracker
{
private:
    Vector<OrderEvent*> m_orders;
public:
    OrderEventTracker(TradingPool* pool) : OrderTracker(pool) {}

   virtual void      onStart() override
   {
        m_orders.clear();
   }
   virtual void      onChange(const Order *oldOrder,const Order *newOrder) override
   {
        m_orders.push(new OrderEvent(ORDER_TRACKED_MODIFIED, newOrder));
   }
   virtual void      onNew(const Order *order) override
   {
        m_orders.push(new OrderEvent(ORDER_TRACKED_NEW, order));
   }
   virtual void      onActivation(const Order *pendingOrder,const Order *marketOrder) override
   {
        m_orders.push(new OrderEvent(ORDER_TRACKED_ACTIVATED, marketOrder));
   }
   virtual void      onClose(const Order *order) override
   {
        m_orders.push(new OrderEvent(ORDER_TRACKED_CLOSED, order));
   }
   virtual void      onPartialClose(const Order *oldOrder,const Order *newOrder) override
   {
        m_orders.push(new OrderEvent(ORDER_TRACKED_PARTIALLY_CLOSED, newOrder));
   }
   int getEvents(OrderEventQueue& queue)
   {
        queue.clear();
        for (int i = 0; i < m_orders.size(); i++)
        {
            queue.Enqueue(m_orders[i]);
        }
        return queue.size();
   }
};